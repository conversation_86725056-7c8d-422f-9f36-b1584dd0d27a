#Requires AutoHotkey v2

/*
=============================================================================
CoppeliaSim 最小化监控系统
=============================================================================
最简单的版本，用于测试基本功能是否正常
=============================================================================
*/

; 检查 UIA 库是否存在
if !FileExist("..\UIA-v2-1.1.0\Lib\UIA.ahk") {
    MsgBox("错误：找不到 UIA 库文件`n路径：..\UIA-v2-1.1.0\Lib\UIA.ahk`n`n请确保 UIA-v2-1.1.0 文件夹在正确位置")
    ExitApp
}

; 尝试加载 UIA 库
try {
    #include ..\UIA-v2-1.1.0\Lib\UIA.ahk
    MsgBox("UIA 库加载成功！")
} catch Error as e {
    MsgBox("UIA 库加载失败：" . e.Message)
    ExitApp
}

; 全局变量
global isMonitoring := false
global operationCount := 0
global logFile := "CoppeliaSim_Minimal_Log.txt"

; 初始化日志
FileDelete(logFile)
FileAppend("=== CoppeliaSim 最小化监控日志 ===`n", logFile, "UTF-8")
FileAppend("开始时间: " . FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss") . "`n`n", logFile, "UTF-8")

; 检查 CoppeliaSim
CheckCoppeliaSim()

; 创建简单界面
CreateMinimalInterface()

return

; 检查 CoppeliaSim
CheckCoppeliaSim() {
    if !WinExist("ahk_exe coppeliasim.exe") {
        result := MsgBox("未检测到 CoppeliaSim 运行`n`n请先启动 CoppeliaSim，然后点击确定继续", "提示", "OKCancel")
        if result = "Cancel" {
            ExitApp
        }
        
        ; 等待用户启动 CoppeliaSim
        Loop {
            Sleep(1000)
            if WinExist("ahk_exe coppeliasim.exe") {
                MsgBox("检测到 CoppeliaSim！")
                break
            }
        }
    } else {
        MsgBox("检测到 CoppeliaSim 正在运行")
    }
}

; 创建最小化界面
CreateMinimalInterface() {
    ; 创建主窗口
    gui := Gui("+Resize", "CoppeliaSim 最小化监控")
    gui.SetFont("s10")
    
    ; 状态显示
    gui.Add("Text", "x10 y10", "监控状态:")
    statusText := gui.Add("Text", "x80 y10 w100 c0xFF0000", "未开始")
    statusText.Name := "StatusText"
    
    gui.Add("Text", "x10 y35", "操作计数:")
    countText := gui.Add("Text", "x80 y35 w100", "0")
    countText.Name := "CountText"
    
    ; 控制按钮
    startBtn := gui.Add("Button", "x200 y10 w80 h25", "开始监控")
    startBtn.OnEvent("Click", (*) => StartMonitoring(gui))
    
    stopBtn := gui.Add("Button", "x290 y10 w80 h25", "停止监控")
    stopBtn.OnEvent("Click", (*) => StopMonitoring(gui))
    
    testBtn := gui.Add("Button", "x380 y10 w80 h25", "测试UIA")
    testBtn.OnEvent("Click", (*) => TestUIA())
    
    ; 日志显示
    gui.Add("Text", "x10 y60", "操作日志:")
    listView := gui.Add("ListView", "x10 y80 w550 h250", ["时间", "操作", "详情"])
    listView.Name := "ListView"
    
    ; 设置列宽
    listView.ModifyCol(1, 80)
    listView.ModifyCol(2, 120)
    listView.ModifyCol(3, 330)
    
    ; 说明文字
    gui.Add("Text", "x10 y340", "说明：点击'测试UIA'验证功能，然后点击'开始监控'开始监控 CoppeliaSim 操作")
    
    ; 显示界面
    gui.Show("w570 h370")
    
    ; 设置事件
    gui.OnEvent("Close", (*) => ExitApp)
    
    ; 保存GUI引用
    global mainGui := gui
}

; 开始监控
StartMonitoring(gui) {
    global isMonitoring
    
    if !WinExist("ahk_exe coppeliasim.exe") {
        MsgBox("CoppeliaSim 未运行，无法开始监控")
        return
    }
    
    if !isMonitoring {
        isMonitoring := true
        SetTimer(MonitorOperations, 300)
        
        ; 更新状态
        statusControl := gui["StatusText"]
        statusControl.Text := "监控中"
        statusControl.Opt("c0x00FF00")
        
        AddLog("系统", "监控已开始")
    }
}

; 停止监控
StopMonitoring(gui) {
    global isMonitoring
    
    if isMonitoring {
        isMonitoring := false
        SetTimer(MonitorOperations, 0)
        
        ; 更新状态
        statusControl := gui["StatusText"]
        statusControl.Text := "已停止"
        statusControl.Opt("c0xFF0000")
        
        AddLog("系统", "监控已停止")
    }
}

; 测试 UIA 功能
TestUIA() {
    try {
        if !WinExist("ahk_exe coppeliasim.exe") {
            MsgBox("请先启动 CoppeliaSim")
            return
        }
        
        ; 获取窗口句柄
        hwnd := WinExist("ahk_exe coppeliasim.exe")
        AddLog("测试", "窗口句柄: " . hwnd)
        
        ; 获取 UIA 元素
        element := UIA.ElementFromHandle(hwnd)
        AddLog("测试", "UIA 元素获取成功")
        
        ; 获取元素信息
        elementName := element.Name ? element.Name : "[无名称]"
        elementType := element.LocalizedType ? element.LocalizedType : "[无类型]"
        AddLog("测试", "元素名称: " . elementName . ", 类型: " . elementType)
        
        ; 测试查找按钮
        try {
            buttons := element.FindElements({Type:"Button"})
            AddLog("测试", "找到 " . buttons.Length . " 个按钮")
        } catch {
            AddLog("测试", "查找按钮失败")
        }
        
        MsgBox("UIA 测试完成，请查看日志")
        
    } catch Error as e {
        AddLog("测试", "UIA 测试失败: " . e.Message)
        MsgBox("UIA 测试失败: " . e.Message)
    }
}

; 监控操作
MonitorOperations() {
    global isMonitoring
    
    if !isMonitoring
        return
        
    static lastClickTime := 0
    
    ; 简单的鼠标点击监控
    if GetKeyState("LButton", "P") {
        currentTime := A_TickCount
        if (currentTime - lastClickTime > 500) {
            MouseGetPos(&x, &y, &winId)
            
            ; 检查是否在 CoppeliaSim 窗口内
            if WinExist("ahk_id " . winId) && WinGetProcessName("ahk_id " . winId) = "coppeliasim.exe" {
                try {
                    ; 尝试获取点击的元素
                    clickedElement := UIA.ElementFromPoint(x, y)
                    if clickedElement {
                        elementName := clickedElement.Name ? clickedElement.Name : "[无名称]"
                        elementType := clickedElement.LocalizedType ? clickedElement.LocalizedType : "[无类型]"
                        
                        AddLog("鼠标点击", elementName . " (" . elementType . ") 位置:(" . x . "," . y . ")")
                    } else {
                        AddLog("鼠标点击", "未获取到元素 位置:(" . x . "," . y . ")")
                    }
                } catch Error as e {
                    AddLog("鼠标点击", "获取元素失败: " . e.Message . " 位置:(" . x . "," . y . ")")
                }
            }
            
            lastClickTime := currentTime
        }
    }
}

; 添加日志
AddLog(operation, details) {
    global operationCount, logFile, mainGui
    
    operationCount++
    currentTime := FormatTime(A_Now, "HH:mm:ss")
    
    ; 添加到界面
    try {
        listView := mainGui["ListView"]
        listView.Add(, currentTime, operation, details)
        listView.Modify(listView.GetCount(), "Vis")
        
        ; 更新计数
        countControl := mainGui["CountText"]
        countControl.Text := operationCount
    } catch {
        ; 忽略界面更新错误
    }
    
    ; 写入日志文件
    logEntry := currentTime . " | " . operation . " | " . details . "`n"
    FileAppend(logEntry, logFile, "UTF-8")
}
