#Requires AutoHotkey v2
#include ..\UIA-v2-1.1.0\Lib\UIA.ahk

; 高级 CoppeliaSim 监控脚本
; 可以启动、停止仿真，并监控仿真状态

global coppliaElement := ""
global isSimulationRunning := false

; 启动 CoppeliaSim（如果未运行）
if !WinExist("ahk_exe coppeliasim.exe") {
    MsgBox("正在启动 CoppeliaSim...")
    Run("C:\Program Files\CoppeliaRobotics\CoppeliaSimEdu\coppeliasim.exe")
    WinWaitActive("ahk_exe coppeliasim.exe")
    Sleep(3000)  ; 等待程序完全加载
}

; 初始化
InitializeCoppelia()

; 设置热键
F1::StartSimulation()      ; F1 开始仿真
F2::StopSimulation()       ; F2 停止仿真
F3::CheckSimulationStatus() ; F3 检查仿真状态
F4::ShowSimulationControls() ; F4 显示所有仿真控件
F5::ExitApp               ; F5 退出

MsgBox("CoppeliaSim 监控脚本已启动！`n`n热键说明：`nF1 - 开始仿真`nF2 - 停止仿真`nF3 - 检查仿真状态`nF4 - 显示仿真控件`nF5 - 退出脚本")

return

; 初始化 CoppeliaSim 元素
InitializeCoppelia() {
    global coppliaElement
    try {
        hwnd := WinExist("ahk_exe coppeliasim.exe")
        if !hwnd {
            MsgBox("未找到 CoppeliaSim 窗口")
            ExitApp
        }
        coppliaElement := UIA.ElementFromHandle(hwnd)
        MsgBox("CoppeliaSim 初始化成功")
    } catch Error as e {
        MsgBox("初始化失败: " . e.Message)
        ExitApp
    }
}

; 开始仿真
StartSimulation() {
    global coppliaElement, isSimulationRunning
    try {
        if !coppliaElement {
            InitializeCoppelia()
        }
        
        ; 查找开始仿真按钮
        startBtn := coppliaElement.FindElement({Type:"CheckBox", Name:"Start/resume simulation"})
        
        if startBtn {
            startBtn.Click()
            isSimulationRunning := true
            MsgBox("仿真已开始！")
        } else {
            MsgBox("未找到开始仿真按钮")
        }
    } catch Error as e {
        MsgBox("开始仿真失败: " . e.Message)
    }
}

; 停止仿真
StopSimulation() {
    global coppliaElement, isSimulationRunning
    try {
        if !coppliaElement {
            InitializeCoppelia()
        }
        
        ; 查找停止仿真按钮
        stopBtn := coppliaElement.FindElement({Type:"Button", Name:"Stop simulation"})
        
        if stopBtn {
            stopBtn.Click()
            isSimulationRunning := false
            MsgBox("仿真已停止！")
        } else {
            MsgBox("未找到停止仿真按钮")
        }
    } catch Error as e {
        MsgBox("停止仿真失败: " . e.Message)
    }
}

; 检查仿真状态
CheckSimulationStatus() {
    global coppliaElement
    try {
        if !coppliaElement {
            InitializeCoppelia()
        }
        
        ; 检查标题栏中的仿真状态
        titleBar := coppliaElement.FindElement({Type:"TitleBar"})
        if titleBar {
            titleText := titleBar.Value
            if InStr(titleText, "SIMULATION STOPPED") {
                MsgBox("仿真状态: 已停止`n标题: " . titleText)
            } else if InStr(titleText, "SIMULATION") {
                MsgBox("仿真状态: 运行中`n标题: " . titleText)
            } else {
                MsgBox("仿真状态: 未知`n标题: " . titleText)
            }
        } else {
            MsgBox("无法获取仿真状态")
        }
    } catch Error as e {
        MsgBox("检查状态失败: " . e.Message)
    }
}

; 显示所有仿真相关控件
ShowSimulationControls() {
    global coppliaElement
    try {
        if !coppliaElement {
            InitializeCoppelia()
        }
        
        ; 查找仿真相关的控件
        controls := ""
        
        ; 查找开始仿真按钮
        try {
            startBtn := coppliaElement.FindElement({Type:"CheckBox", Name:"Start/resume simulation"})
            controls .= "✓ 开始仿真按钮: 已找到`n"
        } catch {
            controls .= "✗ 开始仿真按钮: 未找到`n"
        }
        
        ; 查找停止仿真按钮
        try {
            stopBtn := coppliaElement.FindElement({Type:"Button", Name:"Stop simulation"})
            controls .= "✓ 停止仿真按钮: 已找到`n"
        } catch {
            controls .= "✗ 停止仿真按钮: 未找到`n"
        }
        
        ; 查找暂停仿真按钮
        try {
            pauseBtn := coppliaElement.FindElement({Type:"CheckBox", Name:"Suspend simulation"})
            controls .= "✓ 暂停仿真按钮: 已找到`n"
        } catch {
            controls .= "✗ 暂停仿真按钮: 未找到`n"
        }
        
        MsgBox("仿真控件状态:`n`n" . controls)
        
    } catch Error as e {
        MsgBox("显示控件失败: " . e.Message)
    }
}
