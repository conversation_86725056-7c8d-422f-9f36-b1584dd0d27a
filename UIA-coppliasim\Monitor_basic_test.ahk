#Requires AutoHotkey v2
#include ..\UIA-v2-1.1.0\Lib\UIA.ahk

/*
=============================================================================
CoppeliaSim 基础测试监控
=============================================================================
最基础的测试版本，验证 UIA 库是否正常工作
=============================================================================
*/

; 显示启动信息
MsgBox("CoppeliaSim 基础测试监控启动`n`n这个版本用于测试 UIA 库是否正常工作")

; 全局变量
global isMonitoring := false
global operationCount := 0

; 检查 CoppeliaSim
if !WinExist("ahk_exe coppeliasim.exe") {
    result := MsgBox("未检测到 CoppeliaSim`n`n请先启动 CoppeliaSim，然后点击确定", "提示", "OKCancel")
    if result = "Cancel" {
        ExitApp
    }
    
    ; 等待 CoppeliaSim 启动
    MsgBox("请启动 CoppeliaSim，启动后点击确定")
    
    ; 检查是否启动
    if !WinExist("ahk_exe coppeliasim.exe") {
        MsgBox("仍未检测到 CoppeliaSim，程序退出")
        ExitApp
    }
}

MsgBox("检测到 CoppeliaSim，开始测试 UIA 功能")

; 测试 UIA 基本功能
TestBasicUIA()

; 创建简单界面
CreateBasicInterface()

return

; 测试基本 UIA 功能
TestBasicUIA() {
    try {
        ; 获取窗口句柄
        hwnd := WinExist("ahk_exe coppeliasim.exe")
        MsgBox("步骤1：获取窗口句柄成功: " . hwnd)
        
        ; 获取 UIA 元素
        element := UIA.ElementFromHandle(hwnd)
        MsgBox("步骤2：获取 UIA 元素成功")
        
        ; 获取元素基本信息
        elementName := element.Name ? element.Name : "[无名称]"
        elementType := element.LocalizedType ? element.LocalizedType : "[无类型]"
        MsgBox("步骤3：元素信息获取成功`n名称: " . elementName . "`n类型: " . elementType)
        
        ; 测试鼠标位置元素获取
        MouseGetPos(&x, &y)
        try {
            pointElement := UIA.ElementFromPoint(x, y)
            pointName := pointElement.Name ? pointElement.Name : "[无名称]"
            MsgBox("步骤4：鼠标位置元素获取成功`n位置: (" . x . "," . y . ")`n元素: " . pointName)
        } catch {
            MsgBox("步骤4：鼠标位置元素获取失败（这是正常的）")
        }
        
        MsgBox("UIA 基本功能测试完成！所有功能正常。")
        
    } catch Error as e {
        MsgBox("UIA 测试失败: " . e.Message . "`n行号: " . e.Line)
        ExitApp
    }
}

; 创建基础界面
CreateBasicInterface() {
    ; 创建主窗口
    gui := Gui("+Resize", "CoppeliaSim 基础测试监控")
    gui.SetFont("s10")
    
    ; 说明文字
    gui.Add("Text", "x10 y10 w400", "UIA 测试成功！现在可以测试监控功能：")
    
    ; 状态显示
    gui.Add("Text", "x10 y40", "监控状态:")
    statusText := gui.Add("Text", "x80 y40 w100 c0xFF0000", "未开始")
    statusText.Name := "StatusText"
    
    gui.Add("Text", "x10 y65", "点击计数:")
    countText := gui.Add("Text", "x80 y65 w100", "0")
    countText.Name := "CountText"
    
    ; 控制按钮
    startBtn := gui.Add("Button", "x10 y90 w100 h30", "开始监控")
    startBtn.OnEvent("Click", (*) => StartBasicMonitoring(gui))
    
    stopBtn := gui.Add("Button", "x120 y90 w100 h30", "停止监控")
    stopBtn.OnEvent("Click", (*) => StopBasicMonitoring(gui))
    
    ; 日志显示
    gui.Add("Text", "x10 y130", "监控日志:")
    listView := gui.Add("ListView", "x10 y150 w480 h200", ["时间", "操作", "元素", "位置"])
    listView.Name := "ListView"
    
    ; 设置列宽
    listView.ModifyCol(1, 80)
    listView.ModifyCol(2, 100)
    listView.ModifyCol(3, 200)
    listView.ModifyCol(4, 100)
    
    ; 说明
    gui.Add("Text", "x10 y360", "说明：点击'开始监控'后，在 CoppeliaSim 中点击任意位置进行测试")
    
    ; 显示界面
    gui.Show("w500 h390")
    
    ; 设置事件
    gui.OnEvent("Close", (*) => ExitApp)
    
    ; 保存GUI引用
    global mainGui := gui
}

; 开始基础监控
StartBasicMonitoring(gui) {
    global isMonitoring
    
    if !isMonitoring {
        isMonitoring := true
        SetTimer(BasicMonitor, 200)
        
        ; 更新状态
        statusControl := gui["StatusText"]
        statusControl.Text := "监控中"
        statusControl.Opt("c0x00FF00")
        
        AddBasicLog("系统", "监控开始", "", "")
    }
}

; 停止基础监控
StopBasicMonitoring(gui) {
    global isMonitoring
    
    if isMonitoring {
        isMonitoring := false
        SetTimer(BasicMonitor, 0)
        
        ; 更新状态
        statusControl := gui["StatusText"]
        statusControl.Text := "已停止"
        statusControl.Opt("c0xFF0000")
        
        AddBasicLog("系统", "监控停止", "", "")
    }
}

; 基础监控函数
BasicMonitor() {
    global isMonitoring
    
    if !isMonitoring
        return
        
    static lastClickTime := 0
    static wasPressed := false
    
    ; 检测鼠标点击
    isPressed := GetKeyState("LButton", "P")
    
    if !wasPressed && isPressed {
        ; 鼠标刚按下
        wasPressed := true
    } else if wasPressed && !isPressed {
        ; 鼠标刚释放，这是一个完整的点击
        currentTime := A_TickCount
        if (currentTime - lastClickTime > 300) {
            MouseGetPos(&x, &y, &winId)
            
            ; 检查是否在 CoppeliaSim 窗口内
            if WinExist("ahk_id " . winId) && WinGetProcessName("ahk_id " . winId) = "coppeliasim.exe" {
                try {
                    ; 获取点击的元素
                    clickedElement := UIA.ElementFromPoint(x, y)
                    if clickedElement {
                        elementName := clickedElement.Name ? clickedElement.Name : "[无名称]"
                        elementType := clickedElement.LocalizedType ? clickedElement.LocalizedType : "[无类型]"
                        
                        AddBasicLog("鼠标点击", elementName, elementType, "(" . x . "," . y . ")")
                    } else {
                        AddBasicLog("鼠标点击", "[未获取到元素]", "", "(" . x . "," . y . ")")
                    }
                } catch Error as e {
                    AddBasicLog("鼠标点击", "[获取失败]", e.Message, "(" . x . "," . y . ")")
                }
            }
            
            lastClickTime := currentTime
        }
        wasPressed := false
    }
}

; 添加基础日志
AddBasicLog(operation, element, type, position) {
    global operationCount, mainGui
    
    operationCount++
    currentTime := FormatTime(A_Now, "HH:mm:ss")
    
    ; 添加到界面
    try {
        listView := mainGui["ListView"]
        listView.Add(, currentTime, operation, element, position)
        listView.Modify(listView.GetCount(), "Vis")
        
        ; 更新计数
        countControl := mainGui["CountText"]
        countControl.Text := operationCount
    } catch {
        ; 忽略界面更新错误
    }
    
    ; 写入日志文件
    logEntry := currentTime . " | " . operation . " | " . element . " | " . type . " | " . position . "`n"
    FileAppend(logEntry, "CoppeliaSim_Basic_Test_Log.txt", "UTF-8")
}
