#Requires AutoHotkey v2
#include ..\UIA-v2-1.1.0\Lib\UIA.ahk

/*
=============================================================================
CoppeliaSim 稳定版操作监控系统
=============================================================================
这是一个简化但稳定的监控版本，避免了复杂的事件处理
主要通过定时器和鼠标/键盘监控来实现操作检测

功能：
1. 实时监控鼠标点击操作
2. 监控键盘操作
3. 检测仿真状态变化
4. 生成操作日志和脚本建议
5. 提供简洁的图形界面

热键：
F9  - 开始/停止监控
F10 - 显示统计信息
F11 - 清空日志
F12 - 退出程序
=============================================================================
*/

; 全局变量
global monitor := {
    active: false,
    coppliaElement: "",
    operationCount: 0,
    logFile: "CoppeliaSim_Stable_Monitor_Log.txt",
    gui: "",
    listView: ""
}

; 初始化
InitializeStableMonitor()

; 设置热键
F9::ToggleMonitoring()
F10::ShowStatistics()
F11::ClearLog()
F12::ExitMonitor()

; 显示主界面
ShowMonitorInterface()

return

; 初始化监控系统
InitializeStableMonitor() {
    global monitor
    
    ; 检查 CoppeliaSim
    if !WinExist("ahk_exe coppeliasim.exe") {
        result := MsgBox("CoppeliaSim 未运行，是否启动？", "稳定监控系统", "YesNo")
        if result = "Yes" {
            Run("C:\Program Files\CoppeliaRobotics\CoppeliaSimEdu\coppeliasim.exe")
            WinWaitActive("ahk_exe coppeliasim.exe", , 30)
            Sleep(3000)
        } else {
            ExitApp
        }
    }
    
    try {
        hwnd := WinExist("ahk_exe coppeliasim.exe")
        monitor.coppliaElement := UIA.ElementFromHandle(hwnd)
        
        ; 初始化日志文件
        FileAppend("=== CoppeliaSim 稳定监控日志 ===`n", monitor.logFile)
        FileAppend("监控开始时间: " . A_Now . "`n`n", monitor.logFile)
        
        AddLog("稳定监控系统初始化成功")
        
    } catch Error as e {
        MsgBox("初始化失败: " . e.Message)
        ExitApp
    }
}

; 显示监控界面
ShowMonitorInterface() {
    global monitor
    
    ; 创建主界面
    monitor.gui := Gui("+Resize", "CoppeliaSim 稳定监控系统")
    monitor.gui.SetFont("s10")
    
    ; 状态显示
    monitor.gui.Add("Text", "x10 y10", "监控状态:")
    statusText := monitor.gui.Add("Text", "x80 y10 w100 c0xFF0000", "未开始")
    statusText.Name := "StatusText"
    
    monitor.gui.Add("Text", "x10 y35", "操作计数:")
    countText := monitor.gui.Add("Text", "x80 y35 w100", "0")
    countText.Name := "CountText"
    
    ; 控制按钮
    startBtn := monitor.gui.Add("Button", "x200 y10 w80 h25", "开始监控")
    startBtn.OnEvent("Click", (*) => ToggleMonitoring())
    
    clearBtn := monitor.gui.Add("Button", "x290 y10 w80 h25", "清空日志")
    clearBtn.OnEvent("Click", (*) => ClearLog())
    
    statsBtn := monitor.gui.Add("Button", "x380 y10 w80 h25", "显示统计")
    statsBtn.OnEvent("Click", (*) => ShowStatistics())
    
    ; 日志显示
    monitor.gui.Add("Text", "x10 y60", "操作日志:")
    monitor.listView := monitor.gui.Add("ListView", "x10 y80 w550 h250", ["时间", "操作类型", "元素名称", "脚本建议"])
    
    ; 设置列宽
    monitor.listView.ModifyCol(1, 80)   ; 时间
    monitor.listView.ModifyCol(2, 100)  ; 操作类型
    monitor.listView.ModifyCol(3, 150)  ; 元素名称
    monitor.listView.ModifyCol(4, 200)  ; 脚本建议
    
    ; 状态栏
    monitor.gui.Add("Text", "x10 y340", "热键: F9-开始/停止 | F10-统计 | F11-清空 | F12-退出")
    
    ; 显示界面
    monitor.gui.Show("w570 h370")
    
    ; 设置关闭事件
    monitor.gui.OnEvent("Close", (*) => ExitMonitor())
}

; 开始/停止监控
ToggleMonitoring() {
    global monitor
    
    if !monitor.active {
        ; 开始监控
        try {
            StartStableMonitoring()
            monitor.active := true
            UpdateStatus("监控中", 0x00FF00)
            AddLog("开始稳定监控")
        } catch Error as e {
            MsgBox("启动监控失败: " . e.Message)
        }
    } else {
        ; 停止监控
        StopStableMonitoring()
        monitor.active := false
        UpdateStatus("已停止", 0xFF0000)
        AddLog("停止监控")
    }
}

; 开始稳定监控
StartStableMonitoring() {
    ; 启动定时器监控
    SetTimer(MonitorMouseOperations, 150)
    SetTimer(MonitorKeyboardOperations, 100)
    SetTimer(MonitorSimulationStatus, 500)
}

; 停止稳定监控
StopStableMonitoring() {
    ; 停止所有定时器
    SetTimer(MonitorMouseOperations, 0)
    SetTimer(MonitorKeyboardOperations, 0)
    SetTimer(MonitorSimulationStatus, 0)
}

; 监控鼠标操作
MonitorMouseOperations() {
    global monitor
    
    if !monitor.active
        return
        
    static lastClickTime := 0
    static lastClickElement := ""
    
    ; 检查鼠标左键点击
    if GetKeyState("LButton", "P") {
        currentTime := A_TickCount
        if (currentTime - lastClickTime > 300) { ; 防止重复检测
            MouseGetPos(&x, &y, &winId)
            
            ; 检查是否在 CoppeliaSim 窗口内
            if WinExist("ahk_id " . winId) && WinGetProcessName("ahk_id " . winId) = "coppeliasim.exe" {
                try {
                    ; 获取点击的元素
                    clickedElement := UIA.ElementFromPoint(x, y)
                    if clickedElement {
                        elementName := clickedElement.Name ? clickedElement.Name : "[无名称]"
                        elementType := clickedElement.LocalizedType ? clickedElement.LocalizedType : "未知"
                        
                        ; 避免重复记录相同元素
                        if elementName != lastClickElement {
                            AnalyzeClickedElement(clickedElement, elementName, elementType, x, y)
                            lastClickElement := elementName
                        }
                    }
                } catch {
                    ; 忽略获取元素失败
                }
            }
            
            lastClickTime := currentTime
        }
    } else {
        lastClickElement := "" ; 重置，允许再次点击相同元素
    }
}

; 监控键盘操作
MonitorKeyboardOperations() {
    global monitor
    
    if !monitor.active
        return
        
    static lastKeyTime := 0
    static importantKeys := ["Enter", "Space", "Delete", "Escape", "F1", "F2", "F3", "F4", "F5"]
    
    ; 检查重要按键
    for key in importantKeys {
        if GetKeyState(key, "P") {
            currentTime := A_TickCount
            if (currentTime - lastKeyTime > 400) {
                if WinActive("ahk_exe coppeliasim.exe") {
                    AnalyzeKeyPress(key)
                    lastKeyTime := currentTime
                }
            }
        }
    }
}

; 监控仿真状态
MonitorSimulationStatus() {
    global monitor
    
    if !monitor.active
        return
        
    static lastSimulationState := ""
    
    try {
        if WinExist("ahk_exe coppeliasim.exe") {
            windowTitle := WinGetTitle("ahk_exe coppeliasim.exe")
            
            ; 检测仿真状态变化
            if InStr(windowTitle, "SIMULATION STOPPED") {
                if lastSimulationState != "STOPPED" {
                    LogOperation("状态变化", "仿真停止", "sim.stopSimulation()")
                    lastSimulationState := "STOPPED"
                }
            } else if InStr(windowTitle, "fps") { ; 通常运行时会显示fps
                if lastSimulationState != "RUNNING" {
                    LogOperation("状态变化", "仿真运行", "sim.startSimulation()")
                    lastSimulationState := "RUNNING"
                }
            }
        }
    } catch {
        ; 忽略错误
    }
}

; 分析点击的元素
AnalyzeClickedElement(element, elementName, elementType, x, y) {
    scriptSuggestion := ""
    
    ; 根据元素名称分析对应的脚本
    if InStr(elementName, "Start") && InStr(elementName, "simulation") {
        scriptSuggestion := "sim.startSimulation()"
    } else if InStr(elementName, "Stop") && InStr(elementName, "simulation") {
        scriptSuggestion := "sim.stopSimulation()"
    } else if InStr(elementName, "Suspend") && InStr(elementName, "simulation") {
        scriptSuggestion := "sim.pauseSimulation(true)"
    } else if InStr(elementName, "Camera") {
        scriptSuggestion := "sim.setCameraMatrix(cameraHandle, matrix)"
    } else if InStr(elementName, "selection") || InStr(elementName, "Selection") {
        scriptSuggestion := "sim.getObjectSelection()"
    } else if element.Type = UIA.Type.MenuItem {
        scriptSuggestion := AnalyzeMenuItem(elementName)
    } else if InStr(elementName, "Undo") {
        scriptSuggestion := "-- 撤销操作"
    } else if InStr(elementName, "Redo") {
        scriptSuggestion := "-- 重做操作"
    } else {
        scriptSuggestion := "-- " . elementType . "操作"
    }
    
    LogOperation("鼠标点击", elementName, scriptSuggestion)
}

; 分析菜单项
AnalyzeMenuItem(menuName) {
    switch menuName {
        case "File":
            return "sim.loadScene() / sim.saveScene()"
        case "Add":
            return "sim.createPrimitiveShape()"
        case "Simulation":
            return "sim.setSimulationTimeStep()"
        case "Tools":
            return "-- 工具菜单操作"
        case "Plugins":
            return "sim.loadPlugin('pluginName')"
        default:
            return "-- 菜单操作: " . menuName
    }
}

; 分析按键操作
AnalyzeKeyPress(key) {
    scriptSuggestion := ""
    
    switch key {
        case "Enter":
            scriptSuggestion := "-- 确认操作"
        case "Space":
            scriptSuggestion := "-- 空格键操作"
        case "Delete":
            scriptSuggestion := "sim.removeObject(objectHandle)"
        case "Escape":
            scriptSuggestion := "-- 取消操作"
        default:
            scriptSuggestion := "-- 按键: " . key
    }
    
    LogOperation("按键操作", key, scriptSuggestion)
}

; 记录操作
LogOperation(operationType, elementName, scriptSuggestion) {
    global monitor
    
    monitor.operationCount++
    currentTime := FormatTime(A_Now, "HH:mm:ss")
    
    ; 添加到界面
    if monitor.listView {
        monitor.listView.Add(, currentTime, operationType, elementName, scriptSuggestion)
        monitor.listView.Modify(monitor.listView.GetCount(), "Vis")
    }
    
    ; 写入日志文件
    logEntry := currentTime . " | " . operationType . " | " . elementName . " | " . scriptSuggestion . "`n"
    FileAppend(logEntry, monitor.logFile)
    
    ; 更新计数
    UpdateOperationCount()
}

; 添加系统日志
AddLog(message) {
    LogOperation("系统", "监控系统", message)
}

; 更新状态显示
UpdateStatus(status, color) {
    global monitor
    
    if monitor.gui {
        try {
            statusControl := monitor.gui["StatusText"]
            statusControl.Text := status
            statusControl.Opt("c" . Format("0x{:06X}", color))
        } catch {
            ; 忽略更新错误
        }
    }
}

; 更新操作计数
UpdateOperationCount() {
    global monitor
    
    if monitor.gui {
        try {
            countControl := monitor.gui["CountText"]
            countControl.Text := monitor.operationCount
        } catch {
            ; 忽略更新错误
        }
    }
}

; 显示统计信息
ShowStatistics() {
    global monitor
    
    if monitor.operationCount = 0 {
        MsgBox("暂无监控数据")
        return
    }
    
    ; 简单统计
    statsText := "=== 监控统计信息 ===`n`n"
    statsText .= "总操作次数: " . monitor.operationCount . "`n"
    statsText .= "日志文件: " . monitor.logFile . "`n`n"
    statsText .= "监控的主要操作类型:`n"
    statsText .= "• 鼠标点击操作`n"
    statsText .= "• 键盘按键操作`n"
    statsText .= "• 仿真状态变化`n"
    statsText .= "• 菜单和工具栏操作`n`n"
    statsText .= "所有操作都记录了对应的脚本建议。"
    
    MsgBox(statsText, "监控统计")
}

; 清空日志
ClearLog() {
    global monitor
    
    result := MsgBox("确定要清空所有监控日志吗？", "确认清空", "YesNo")
    if result = "Yes" {
        ; 清空界面
        if monitor.listView {
            monitor.listView.Delete()
        }
        
        ; 重置计数
        monitor.operationCount := 0
        UpdateOperationCount()
        
        ; 清空日志文件
        try {
            FileDelete(monitor.logFile)
            FileAppend("=== CoppeliaSim 稳定监控日志 ===`n", monitor.logFile)
            FileAppend("日志清空时间: " . A_Now . "`n`n", monitor.logFile)
        } catch {
            ; 忽略文件错误
        }
        
        AddLog("监控日志已清空")
    }
}

; 退出监控
ExitMonitor() {
    global monitor
    
    if monitor.active {
        StopStableMonitoring()
    }
    
    AddLog("监控系统退出")
    ExitApp
}
