#Requires AutoHotkey v2
#include ..\UIA-v2-1.1.0\Lib\UIA.ahk

/*
=============================================================================
CoppeliaSim 实时操作监控系统
=============================================================================
功能：
1. 实时监控用户在 CoppeliaSim 中的所有操作行为
2. 记录点击事件、菜单操作、按钮操作等
3. 分析操作背后的脚本和代码逻辑
4. 生成操作日志和行为分析报告

使用方法：
1. 确保 CoppeliaSim 正在运行
2. 运行此脚本开始监控
3. 在 CoppeliaSim 中进行各种操作
4. 查看实时监控窗口和日志文件

热键：
F9  - 开始/停止监控
F10 - 显示监控统计
F11 - 清空监控日志
F12 - 退出监控程序
=============================================================================
*/

; 全局变量
global monitoringActive := false
global coppliaElement := ""
global logFile := "CoppeliaSim_Monitor_Log.txt"
global monitorGui := ""
global logListView := ""
global operationCount := 0
global lastOperation := ""
global eventHandlers := []

; 初始化监控系统
InitializeMonitor()

; 设置热键
F9::ToggleMonitoring()
F10::ShowStatistics()
F11::ClearLog()
F12::ExitMonitor()

; 显示主界面
ShowMainInterface()

return

; 初始化监控系统
InitializeMonitor() {
    global coppliaElement, logFile

    ; 检查 CoppeliaSim 是否运行
    if !WinExist("ahk_exe coppeliasim.exe") {
        result := MsgBox("CoppeliaSim 未运行，是否启动？", "监控系统", "YesNo")
        if result = "Yes" {
            Run("C:\Program Files\CoppeliaRobotics\CoppeliaSimEdu\coppeliasim.exe")
            WinWaitActive("ahk_exe coppeliasim.exe", , 30)
            Sleep(3000)
        } else {
            ExitApp
        }
    }

    try {
        ; 获取 CoppeliaSim 窗口元素
        hwnd := WinExist("ahk_exe coppeliasim.exe")
        coppliaElement := UIA.ElementFromHandle(hwnd)

        ; 初始化日志文件 - 使用 UTF-8 编码
        FileDelete(logFile)
        FileAppend("=== CoppeliaSim 操作监控日志 ===`n", logFile, "UTF-8")
        FileAppend("监控开始时间: " . FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss") . "`n`n", logFile, "UTF-8")

        AddLog("监控系统初始化成功")

    } catch Error as e {
        MsgBox("初始化失败: " . e.Message)
        ExitApp
    }
}

; 显示主界面
ShowMainInterface() {
    global monitorGui, logListView

    ; 创建监控界面 - 添加最小尺寸限制
    monitorGui := Gui("+Resize +MinSize640x480", "CoppeliaSim 操作监控系统")
    monitorGui.SetFont("s9")

    ; 添加控件
    monitorGui.Add("Text", "x10 y10", "监控状态:")
    statusText := monitorGui.Add("Text", "x80 y10 w100 c0xFF0000", "未开始")
    statusText.Name := "StatusText"

    monitorGui.Add("Text", "x10 y35", "操作计数:")
    countText := monitorGui.Add("Text", "x80 y35 w100", "0")
    countText.Name := "CountText"

    ; 控制按钮
    startBtn := monitorGui.Add("Button", "x200 y10 w80 h25", "开始监控")
    startBtn.OnEvent("Click", (*) => ToggleMonitoring())
    startBtn.Name := "StartBtn"

    stopBtn := monitorGui.Add("Button", "x290 y10 w80 h25", "停止监控")
    stopBtn.OnEvent("Click", (*) => ToggleMonitoring())
    stopBtn.Name := "StopBtn"

    clearBtn := monitorGui.Add("Button", "x380 y10 w80 h25", "清空日志")
    clearBtn.OnEvent("Click", (*) => ClearLog())

    exportBtn := monitorGui.Add("Button", "x470 y10 w80 h25", "导出日志")
    exportBtn.OnEvent("Click", (*) => ExportDetailedLog())

    ; 日志显示区域 - 改进列标题
    monitorGui.Add("Text", "x10 y60", "操作日志:")
    logListView := monitorGui.Add("ListView", "x10 y80 w700 h350 +Grid", ["时间", "操作类型", "元素名称", "脚本代码", "详细描述"])

    ; 状态栏
    statusBar := monitorGui.Add("Text", "x10 y440", "热键: F9-开始/停止 | F10-统计 | F11-清空 | F12-退出 | 支持窗口拖拽调整大小")
    statusBar.Name := "StatusBar"

    ; 设置列宽 - 优化显示
    logListView.ModifyCol(1, 70)   ; 时间
    logListView.ModifyCol(2, 90)   ; 操作类型
    logListView.ModifyCol(3, 140)  ; 元素名称
    logListView.ModifyCol(4, 200)  ; 脚本代码
    logListView.ModifyCol(5, 180)  ; 详细描述

    ; 显示界面 - 增大默认尺寸
    monitorGui.Show("w720 h470")

    ; 设置窗口事件
    monitorGui.OnEvent("Close", (*) => ExitMonitor())
    monitorGui.OnEvent("Size", OnGuiResize)
}

; 窗口大小调整事件处理
OnGuiResize(GuiObj, MinMax, Width, Height) {
    global logListView

    if MinMax = -1  ; 最小化时不处理
        return

    ; 调整ListView大小以适应窗口
    if logListView {
        newWidth := Width - 30
        newHeight := Height - 130

        ; 确保最小尺寸
        if newWidth < 400
            newWidth := 400
        if newHeight < 200
            newHeight := 200

        logListView.Move(, , newWidth, newHeight)

        ; 调整状态栏位置
        try {
            statusBarControl := GuiObj["StatusBar"]
            statusBarControl.Move(10, Height - 30)
        } catch {
            ; 忽略错误
        }
    }
}

; 导出详细日志
ExportDetailedLog() {
    global operationCount, logFile

    if operationCount = 0 {
        MsgBox("暂无监控数据可导出")
        return
    }

    ; 生成详细报告
    reportFile := "CoppeliaSim_Detailed_Report_" . FormatTime(A_Now, "yyyyMMdd_HHmmss") . ".txt"

    try {
        report := "=== CoppeliaSim 详细操作报告 ===`n"
        report .= "生成时间: " . FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss") . "`n"
        report .= "总操作数量: " . operationCount . "`n`n"

        ; 读取原始日志并格式化
        if FileExist(logFile) {
            logContent := FileRead(logFile)
            report .= "=== 详细操作记录 ===`n"
            report .= logContent
        }

        FileAppend(report, reportFile)
        MsgBox("详细报告已导出到: " . reportFile)

    } catch Error as e {
        MsgBox("导出失败: " . e.Message)
    }
}

; 开始/停止监控
ToggleMonitoring() {
    global monitoringActive, coppliaElement, eventHandlers

    if !monitoringActive {
        ; 开始监控
        try {
            StartEventMonitoring()
            monitoringActive := true
            UpdateStatus("监控中", 0x00FF00)
            AddLog("开始监控 CoppeliaSim 操作")
        } catch Error as e {
            MsgBox("启动监控失败: " . e.Message)
        }
    } else {
        ; 停止监控
        StopEventMonitoring()
        monitoringActive := false
        UpdateStatus("已停止", 0xFF0000)
        AddLog("停止监控")
    }
}

; 开始事件监控
StartEventMonitoring() {
    global coppliaElement, eventHandlers

    try {
        ; 简化监控方式，主要使用定时器监控
        ; UIA 事件处理在某些情况下可能不稳定，所以我们使用更可靠的方法

        ; 启动监控 - 降低频率减少资源占用
        SetTimer(MonitorMouseClicks, 50)      ; 鼠标监控保持较高频率
        SetTimer(MonitorKeyboard, 100)       ; 键盘监控适中频率
        SetTimer(MonitorWindowChanges, 2000)  ; 窗口变化监控降低频率

        AddLog("事件监控已启动")

    } catch Error as e {
        throw Error("启动事件监控失败: " . e.Message)
    }
}

; 停止事件监控
StopEventMonitoring() {
    global eventHandlers

    try {
        ; 停止所有定时器
        SetTimer(MonitorMouseClicks, 0)
        SetTimer(MonitorKeyboard, 0)
        SetTimer(MonitorWindowChanges, 0)

        AddLog("事件监控已停止")

    } catch Error as e {
        AddLog("停止监控时出错: " . e.Message)
    }
}

; 监控窗口变化（优化版 - 减少无意义的记录）
MonitorWindowChanges() {
    global coppliaElement, monitoringActive

    if !monitoringActive
        return

    static lastTitle := ""
    static lastCheckTime := 0

    ; 降低检查频率，避免过度监控
    currentTime := A_TickCount
    if (currentTime - lastCheckTime < 1000) ; 至少间隔1秒
        return
    lastCheckTime := currentTime

    try {
        ; 检查 CoppeliaSim 窗口标题变化（只关注仿真状态变化）
        if WinExist("ahk_exe coppeliasim.exe") {
            currentTitle := WinGetTitle("ahk_exe coppeliasim.exe")
            if currentTitle != lastTitle && lastTitle != "" {
                ; 只记录重要的状态变化
                if InStr(currentTitle, "SIMULATION STOPPED") && !InStr(lastTitle, "SIMULATION STOPPED") {
                    LogDetailedOperation("状态变化", "仿真停止", "sim.stopSimulation()", "仿真状态从运行变为停止")
                } else if !InStr(currentTitle, "SIMULATION STOPPED") && InStr(lastTitle, "SIMULATION STOPPED") {
                    LogDetailedOperation("状态变化", "仿真开始", "sim.startSimulation()", "仿真状态从停止变为运行")
                }
                ; 移除其他不重要的标题变化记录
            }
            lastTitle := currentTitle
        }
    } catch {
        ; 忽略错误
    }
}

; 鼠标点击监控（优化版 - 只监控实际点击事件）
MonitorMouseClicks() {
    global coppliaElement, monitoringActive

    if !monitoringActive
        return

    static lastClickTime := 0
    static lastClickX := 0
    static lastClickY := 0
    static wasPressed := false

    ; 检测鼠标按下和释放事件，只在释放时记录（真正的点击）
    isPressed := GetKeyState("LButton", "P")

    if !wasPressed && isPressed {
        ; 鼠标刚按下，记录位置但不记录操作
        MouseGetPos(&lastClickX, &lastClickY)
        wasPressed := true
    } else if wasPressed && !isPressed {
        ; 鼠标刚释放，这是一个完整的点击操作
        currentTime := A_TickCount
        if (currentTime - lastClickTime > 300) { ; 防止重复检测
            MouseGetPos(&x, &y, &winId)

            ; 检查是否在 CoppeliaSim 窗口内
            if WinExist("ahk_id " . winId) && WinGetProcessName("ahk_id " . winId) = "coppeliasim.exe" {
                ; 检查鼠标移动距离，如果移动太多可能是拖拽而不是点击
                moveDistance := Sqrt(Abs(x - lastClickX)**2 + Abs(y - lastClickY)**2)
                if moveDistance < 10 { ; 移动距离小于10像素才认为是点击
                    try {
                        ; 获取点击位置的元素
                        clickedElement := UIA.ElementFromPoint(x, y)
                        if clickedElement {
                            elementName := clickedElement.Name ? clickedElement.Name : "[无名称]"
                            elementType := clickedElement.LocalizedType ? clickedElement.LocalizedType : "未知类型"
                            elementClass := clickedElement.ClassName ? clickedElement.ClassName : ""
                            elementValue := clickedElement.Value ? clickedElement.Value : ""

                            ; 过滤掉一些不重要的元素
                            if ShouldRecordElement(elementName, elementType, elementClass) {
                                ; 分析操作并生成脚本建议
                                scriptCode := AnalyzeElementForScript(clickedElement, elementName, elementType)
                                detailedInfo := BuildDetailedInfo(elementName, elementType, elementClass, elementValue, x, y)

                                ; 记录详细操作信息
                                LogDetailedOperation("鼠标点击", elementName, scriptCode, detailedInfo)
                            }
                        }
                    } catch {
                        ; 忽略获取元素失败的情况
                    }
                }
            }

            lastClickTime := currentTime
        }
        wasPressed := false
    }
}

; 键盘监控
MonitorKeyboard() {
    global monitoringActive

    if !monitoringActive
        return

    static lastKeyTime := 0
    static monitoredKeys := ["Enter", "Space", "Tab", "Escape", "F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "F10", "F11", "F12"]

    ; 检查特殊按键
    for key in monitoredKeys {
        if GetKeyState(key, "P") {
            currentTime := A_TickCount
            if (currentTime - lastKeyTime > 300) { ; 防止重复检测
                ; 检查当前窗口是否为 CoppeliaSim
                if WinActive("ahk_exe coppeliasim.exe") {
                    scriptCode := AnalyzeKeyScript(key)
                    detailedInfo := "在 CoppeliaSim 中按下 " . key . " 键"
                    LogDetailedOperation("按键操作", key, scriptCode, detailedInfo)
                    lastKeyTime := currentTime
                }
            }
        }
    }
}

; 分析操作行为
AnalyzeOperation(element, elementName) {
    try {
        ; 分析仿真相关操作
        if InStr(elementName, "simulation") || InStr(elementName, "仿真") {
            if InStr(elementName, "Start") || InStr(elementName, "start") {
                LogOperation("仿真控制", "开始仿真", "用户启动了仿真 | 可能的脚本: sim.startSimulation()")
            } else if InStr(elementName, "Stop") || InStr(elementName, "stop") {
                LogOperation("仿真控制", "停止仿真", "用户停止了仿真 | 可能的脚本: sim.stopSimulation()")
            } else if InStr(elementName, "Suspend") || InStr(elementName, "pause") {
                LogOperation("仿真控制", "暂停仿真", "用户暂停了仿真 | 可能的脚本: sim.pauseSimulation()")
            }
        }

        ; 分析菜单操作
        if element.Type = UIA.Type.MenuItem {
            AnalyzeMenuOperation(elementName)
        }

        ; 分析工具栏操作
        if element.Type = UIA.Type.Button || element.Type = UIA.Type.CheckBox {
            AnalyzeToolbarOperation(elementName)
        }

    } catch {
        ; 忽略分析错误
    }
}

; 分析菜单操作
AnalyzeMenuOperation(menuName) {
    switch menuName {
        case "File":
            LogOperation("菜单操作", "文件菜单", "可能操作: 新建、打开、保存场景 | 脚本: sim.loadScene(), sim.saveScene()")
        case "Edit":
            LogOperation("菜单操作", "编辑菜单", "可能操作: 撤销、重做、复制、粘贴 | 脚本: sim.copyPasteObjects()")
        case "Add":
            LogOperation("菜单操作", "添加菜单", "可能操作: 添加对象、形状、传感器 | 脚本: sim.createPrimitiveShape()")
        case "Simulation":
            LogOperation("菜单操作", "仿真菜单", "可能操作: 仿真设置、参数配置 | 脚本: sim.setSimulationTimeStep()")
        case "Tools":
            LogOperation("菜单操作", "工具菜单", "可能操作: 计算模块、脚本编辑 | 脚本: sim.openModule()")
        case "Plugins":
            LogOperation("菜单操作", "插件菜单", "可能操作: 加载插件、插件设置 | 脚本: sim.loadPlugin()")
        default:
            LogOperation("菜单操作", menuName, "用户点击了菜单项")
    }
}

; 分析工具栏操作
AnalyzeToolbarOperation(toolName) {
    if InStr(toolName, "Camera") {
        LogOperation("视图控制", toolName, "相机操作 | 脚本: sim.setCameraMatrix()")
    } else if InStr(toolName, "selection") || InStr(toolName, "Selection") {
        LogOperation("对象操作", toolName, "选择操作 | 脚本: sim.getObjectSelection()")
    } else if InStr(toolName, "shift") || InStr(toolName, "rotate") {
        LogOperation("对象操作", toolName, "对象变换 | 脚本: sim.setObjectPosition(), sim.setObjectOrientation()")
    } else if InStr(toolName, "Undo") {
        LogOperation("编辑操作", "撤销", "撤销上一步操作")
    } else if InStr(toolName, "Redo") {
        LogOperation("编辑操作", "重做", "重做操作")
    }
}

; 分析点击操作
AnalyzeClickOperation(element, elementName) {
    try {
        ; 获取元素的更多信息
        elementClass := element.ClassName ? element.ClassName : ""
        elementValue := element.Value ? element.Value : ""

        ; 分析特定的点击操作
        if InStr(elementClass, "QToolButton") {
            LogOperation("工具按钮", elementName, "工具栏按钮点击 | 类: " . elementClass)
        } else if InStr(elementClass, "QComboBox") {
            LogOperation("下拉选择", elementName, "下拉框操作 | 当前值: " . elementValue)
        } else if InStr(elementClass, "QListWidget") {
            LogOperation("列表操作", elementName, "模型列表操作")
        } else if InStr(elementClass, "QTreeWidget") {
            LogOperation("树形控件", elementName, "场景层次结构操作")
        }

    } catch {
        ; 忽略分析错误
    }
}

; 记录详细操作日志（新版本，支持5列显示）
LogDetailedOperation(operationType, elementName, scriptCode, detailedInfo) {
    global logListView, operationCount, logFile

    operationCount++
    currentTime := FormatTime(A_Now, "HH:mm:ss")

    ; 添加到界面列表（5列：时间、操作类型、元素名称、脚本代码、详细描述）
    if logListView {
        logListView.Add(, currentTime, operationType, elementName, scriptCode, detailedInfo)
        ; 自动滚动到最新项
        logListView.Modify(logListView.GetCount(), "Vis")
    }

    ; 写入详细日志文件 - 使用 UTF-8 编码
    logEntry := "时间: " . currentTime . "`n"
    logEntry .= "操作: " . operationType . "`n"
    logEntry .= "元素: " . elementName . "`n"
    logEntry .= "脚本: " . scriptCode . "`n"
    logEntry .= "详情: " . detailedInfo . "`n"
    logEntry .= "---`n"

    FileAppend(logEntry, logFile, "UTF-8")

    ; 更新计数显示
    UpdateOperationCount()
}

; 兼容旧版本的LogOperation函数
LogOperation(operationType, elementName, details) {
    LogDetailedOperation(operationType, elementName, "-- 操作记录", details)
}

; 分析元素对应的脚本代码
AnalyzeElementForScript(element, elementName, elementType) {
    try {
        ; 根据元素名称和类型分析对应的脚本
        if InStr(elementName, "Start") && InStr(elementName, "simulation") {
            return "sim.startSimulation()"
        } else if InStr(elementName, "Stop") && InStr(elementName, "simulation") {
            return "sim.stopSimulation()"
        } else if InStr(elementName, "Suspend") && InStr(elementName, "simulation") {
            return "sim.pauseSimulation(true)"
        } else if InStr(elementName, "Camera") {
            if InStr(elementName, "pan") {
                return "-- 相机平移操作"
            } else if InStr(elementName, "rotate") {
                return "-- 相机旋转操作"
            } else if InStr(elementName, "shift") {
                return "-- 相机移动操作"
            } else {
                return "sim.setCameraMatrix(cameraHandle, matrix)"
            }
        } else if InStr(elementName, "selection") || InStr(elementName, "Selection") {
            return "sim.getObjectSelection()"
        } else if InStr(elementName, "shift") || InStr(elementName, "rotate") {
            return "sim.setObjectPosition() / sim.setObjectOrientation()"
        } else if element.Type = UIA.Type.MenuItem {
            return AnalyzeMenuItemScript(elementName)
        } else if element.Type = UIA.Type.Button {
            return AnalyzeButtonScript(elementName)
        } else if element.Type = UIA.Type.CheckBox {
            return AnalyzeCheckBoxScript(elementName)
        } else if InStr(elementName, "Undo") {
            return "-- 撤销操作"
        } else if InStr(elementName, "Redo") {
            return "-- 重做操作"
        } else {
            return "-- " . elementType . "操作"
        }
    } catch {
        return "-- 未知操作"
    }
}

; 分析菜单项脚本
AnalyzeMenuItemScript(menuName) {
    switch menuName {
        case "File":
            return "sim.loadScene() / sim.saveScene()"
        case "Edit":
            return "-- 编辑操作"
        case "Add":
            return "sim.createPrimitiveShape(sim.primitiveshape_cuboid, {1,1,1})"
        case "Simulation":
            return "sim.setSimulationTimeStep(0.05)"
        case "Tools":
            return "-- 工具操作"
        case "Plugins":
            return "sim.loadPlugin('pluginName')"
        default:
            return "-- 菜单: " . menuName
    }
}

; 分析按钮脚本
AnalyzeButtonScript(buttonName) {
    if InStr(buttonName, "Fit to view") {
        return "-- 适应视图操作"
    } else if InStr(buttonName, "Assemble") {
        return "-- 组装/拆卸操作"
    } else if InStr(buttonName, "Undo") {
        return "-- 撤销操作"
    } else if InStr(buttonName, "Redo") {
        return "-- 重做操作"
    } else {
        return "-- 按钮: " . buttonName
    }
}

; 分析复选框脚本
AnalyzeCheckBoxScript(checkboxName) {
    if InStr(checkboxName, "Camera") {
        return "-- 相机控制模式切换"
    } else if InStr(checkboxName, "selection") {
        return "-- 选择模式切换"
    } else if InStr(checkboxName, "real-time") {
        return "sim.setRealTimeSimulation(true/false)"
    } else if InStr(checkboxName, "visualization") {
        return "-- 可视化切换"
    } else {
        return "-- 复选框: " . checkboxName
    }
}

; 构建详细信息
BuildDetailedInfo(elementName, elementType, elementClass, elementValue, x, y) {
    detailParts := []

    ; 添加类型信息
    if elementType != "未知类型" && elementType != "" {
        detailParts.Push("类型:" . elementType)
    }

    ; 添加类名信息
    if elementClass != "" {
        detailParts.Push("类:" . elementClass)
    }

    ; 添加值信息
    if elementValue != "" {
        detailParts.Push("值:" . elementValue)
    }

    ; 添加位置信息
    detailParts.Push("位置:(" . x . "," . y . ")")

    ; 组合详细信息
    return StrJoin(detailParts, " | ")
}

; 分析按键对应的脚本
AnalyzeKeyScript(key) {
    switch key {
        case "Enter":
            return "-- 确认操作"
        case "Space":
            return "-- 空格键操作（可能是播放/暂停）"
        case "Tab":
            return "-- 切换焦点"
        case "Escape":
            return "-- 取消/退出操作"
        case "F1":
            return "-- 帮助"
        case "F2":
            return "-- 重命名操作"
        case "F3":
            return "-- 查找下一个"
        case "F4":
            return "-- 可能的窗口操作"
        case "F5":
            return "-- 刷新/重新加载"
        case "F6":
            return "-- 切换面板"
        case "F7":
            return "-- 可能的调试操作"
        case "F8":
            return "-- 可能的步进操作"
        case "F9":
            return "-- 可能的断点操作"
        case "F10":
            return "-- 可能的菜单操作"
        case "F11":
            return "-- 可能的全屏切换"
        case "F12":
            return "-- 可能的开发者工具"
        default:
            return "-- 按键: " . key
    }
}

; 判断是否应该记录该元素（过滤不重要的元素）
ShouldRecordElement(elementName, elementType, elementClass) {
    ; 过滤掉一些不重要或频繁触发的元素

    ; 跳过无名称且类型未知的元素
    if elementName = "[无名称]" && elementType = "未知类型"
        return false

    ; 跳过一些系统级的元素
    if InStr(elementClass, "QSplitter") || InStr(elementClass, "QScrollBar")
        return false

    ; 跳过一些装饰性元素
    if elementType = "分隔符" || elementType = "Separator"
        return false

    ; 跳过空白区域
    if elementName = "" && elementType = "窗格"
        return false

    ; 只记录有意义的交互元素
    meaningfulTypes := ["按钮", "Button", "复选框", "CheckBox", "菜单项", "MenuItem",
                       "工具栏", "ToolBar", "列表项", "ListItem", "树项", "TreeItem",
                       "下拉框", "ComboBox", "编辑框", "Edit"]

    for type in meaningfulTypes {
        if InStr(elementType, type)
            return true
    }

    ; 如果有明确的名称，也记录
    if elementName != "[无名称]" && elementName != ""
        return true

    return false
}

; 字符串连接辅助函数
StrJoin(array, separator) {
    result := ""
    for i, item in array {
        if i > 1
            result .= separator
        result .= item
    }
    return result
}

; 添加日志条目（简化版本）
AddLog(message) {
    LogOperation("系统", "监控系统", message)
}

; 更新状态显示
UpdateStatus(status, color) {
    global monitorGui

    if monitorGui {
        try {
            statusControl := monitorGui["StatusText"]
            statusControl.Text := status
            statusControl.Opt("c" . Format("0x{:06X}", color))
        } catch {
            ; 忽略更新错误
        }
    }
}

; 更新操作计数
UpdateOperationCount() {
    global monitorGui, operationCount

    if monitorGui {
        try {
            countControl := monitorGui["CountText"]
            countControl.Text := operationCount
        } catch {
            ; 忽略更新错误
        }
    }
}

; 显示统计信息
ShowStatistics() {
    global operationCount, logFile

    ; 读取日志文件获取详细统计
    try {
        logContent := FileRead(logFile)
        lines := StrSplit(logContent, "`n")

        ; 统计不同类型的操作
        stats := Map()
        stats["鼠标点击"] := 0
        stats["按键操作"] := 0
        stats["仿真控制"] := 0
        stats["菜单操作"] := 0
        stats["工具按钮"] := 0
        stats["其他操作"] := 0

        for line in lines {
            if InStr(line, "鼠标点击")
                stats["鼠标点击"]++
            else if InStr(line, "按键操作")
                stats["按键操作"]++
            else if InStr(line, "仿真控制")
                stats["仿真控制"]++
            else if InStr(line, "菜单操作")
                stats["菜单操作"]++
            else if InStr(line, "工具按钮")
                stats["工具按钮"]++
            else if InStr(line, "|")
                stats["其他操作"]++
        }

        ; 构建统计信息
        statsText := "=== CoppeliaSim 操作统计 ===`n`n"
        statsText .= "总操作次数: " . operationCount . "`n`n"
        statsText .= "操作类型分布:`n"

        for type, count in stats {
            if count > 0 {
                percentage := Round((count / operationCount) * 100, 1)
                statsText .= "• " . type . ": " . count . " 次 (" . percentage . "%)`n"
            }
        }

        statsText .= "`n日志文件: " . logFile

        MsgBox(statsText, "操作统计")

    } catch Error as e {
        MsgBox("获取统计信息失败: " . e.Message)
    }
}

; 清空日志
ClearLog() {
    global logListView, operationCount, logFile

    result := MsgBox("确定要清空所有监控日志吗？", "确认清空", "YesNo")
    if result = "Yes" {
        ; 清空界面列表
        if logListView {
            logListView.Delete()
        }

        ; 重置计数
        operationCount := 0
        UpdateOperationCount()

        ; 清空日志文件
        try {
            FileDelete(logFile)
            FileAppend("=== CoppeliaSim 操作监控日志 ===`n", logFile)
            FileAppend("日志清空时间: " . A_Now . "`n`n", logFile)
        } catch {
            ; 忽略文件操作错误
        }

        AddLog("监控日志已清空")
    }
}

; 退出监控程序
ExitMonitor() {
    global monitoringActive

    if monitoringActive {
        StopEventMonitoring()
    }

    AddLog("监控系统退出")
    ExitApp
}
