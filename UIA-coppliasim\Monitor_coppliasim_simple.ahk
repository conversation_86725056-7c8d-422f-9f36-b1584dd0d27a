#Requires AutoHotkey v2
#include ..\UIA-v2-1.1.0\Lib\UIA.ahk

; 简化版 CoppeliaSim 监控脚本
; 根据UI结构分析，直接查找正确的开始仿真按钮

; 启动 CoppeliaSim（使用完整路径）
Run("C:\Program Files\CoppeliaRobotics\CoppeliaSimEdu\coppeliasim.exe")
WinWaitActive("ahk_exe coppeliasim.exe")

; 等待程序完全加载
Sleep(3000)

try {
    ; 获取 CoppeliaSim 窗口元素
    hwnd := WinExist("ahk_exe coppeliasim.exe")
    if !hwnd {
        MsgBox("未找到 CoppeliaSim 窗口")
        ExitApp
    }
    
    element := UIA.ElementFromHandle(hwnd)
    
    ; 根据UI结构分析，开始仿真按钮是一个CheckBox，名称为"Start/resume simulation"
    ; 位置：4,21: Type: 50002 (CheckBox) Name: "Start/resume simulation"
    startBtn := element.FindElement({Type:"CheckBox", Name:"Start/resume simulation"})
    
    if startBtn {
        MsgBox("找到开始仿真按钮，准备点击...")
        startBtn.Click()
        MsgBox("已点击开始仿真按钮！`n仿真应该已经开始运行。")
        
        ; 可选：等待一段时间后停止仿真
        ; Sleep(5000)  ; 等待5秒
        ; stopBtn := element.FindElement({Type:"Button", Name:"Stop simulation"})
        ; if stopBtn {
        ;     stopBtn.Click()
        ;     MsgBox("已停止仿真")
        ; }
        
    } else {
        MsgBox("未找到开始仿真按钮")
    }
    
} catch Error as e {
    MsgBox("程序执行出错: " . e.Message . "`n`n请确保：`n1. CoppeliaSim 已完全加载`n2. 程序界面可见且未被遮挡")
}
