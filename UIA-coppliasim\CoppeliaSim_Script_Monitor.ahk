#Requires AutoHotkey v2
#include ..\UIA-v2-1.1.0\Lib\UIA.ahk

/*
=============================================================================
CoppeliaSim 脚本和代码监控分析器
=============================================================================
专门用于监控和分析 CoppeliaSim 操作背后的脚本和代码逻辑

功能特点：
1. 实时监控用户操作
2. 分析操作对应的 Lua 脚本代码
3. 生成代码建议和脚本模板
4. 记录 API 调用序列
5. 导出可执行的脚本代码

使用方法：
1. 启动 CoppeliaSim
2. 运行此监控器
3. 在 CoppeliaSim 中进行操作
4. 查看生成的脚本代码分析

热键：
Ctrl+F9  - 开始/停止监控
Ctrl+F10 - 导出脚本代码
Ctrl+F11 - 显示API调用序列
Ctrl+F12 - 退出程序
=============================================================================
*/

; 全局变量
global scriptMonitor := {
    active: false,
    coppliaElement: "",
    apiCalls: [],
    scriptSequence: [],
    operationHistory: [],
    outputFile: "CoppeliaSim_Generated_Script.lua"
}

; 初始化
InitializeScriptMonitor()

; 设置热键
^F9::ToggleScriptMonitoring()
^F10::ExportGeneratedScript()
^F11::ShowAPISequence()
^F12::ExitScriptMonitor()

; 显示启动信息
MsgBox("CoppeliaSim 脚本监控分析器已启动！`n`n" .
       "热键说明：`n" .
       "Ctrl+F9  - 开始/停止监控`n" .
       "Ctrl+F10 - 导出脚本代码`n" .
       "Ctrl+F11 - 显示API调用序列`n" .
       "Ctrl+F12 - 退出程序`n`n" .
       "请在 CoppeliaSim 中进行操作，系统将自动分析对应的脚本代码。")

return

; 初始化脚本监控器
InitializeScriptMonitor() {
    global scriptMonitor
    
    ; 检查 CoppeliaSim
    if !WinExist("ahk_exe coppeliasim.exe") {
        result := MsgBox("CoppeliaSim 未运行，是否启动？", "脚本监控器", "YesNo")
        if result = "Yes" {
            Run("C:\Program Files\CoppeliaRobotics\CoppeliaSimEdu\coppeliasim.exe")
            WinWaitActive("ahk_exe coppeliasim.exe", , 30)
            Sleep(3000)
        } else {
            ExitApp
        }
    }
    
    try {
        hwnd := WinExist("ahk_exe coppeliasim.exe")
        scriptMonitor.coppliaElement := UIA.ElementFromHandle(hwnd)
        
        ; 初始化输出文件
        InitializeScriptFile()
        
        ToolTip("脚本监控器初始化成功", 10, 10)
        SetTimer(() => ToolTip(), -2000)
        
    } catch Error as e {
        MsgBox("初始化失败: " . e.Message)
        ExitApp
    }
}

; 初始化脚本文件
InitializeScriptFile() {
    global scriptMonitor
    
    scriptHeader := "-- CoppeliaSim 自动生成脚本`n"
    scriptHeader .= "-- 生成时间: " . FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss") . "`n"
    scriptHeader .= "-- 基于用户操作自动分析生成`n`n"
    scriptHeader .= "function sysCall_init()`n"
    scriptHeader .= "    -- 初始化代码`n"
    scriptHeader .= "end`n`n"
    scriptHeader .= "function sysCall_actuation()`n"
    scriptHeader .= "    -- 主循环代码`n"
    
    FileDelete(scriptMonitor.outputFile)
    FileAppend(scriptHeader, scriptMonitor.outputFile)
}

; 开始/停止脚本监控
ToggleScriptMonitoring() {
    global scriptMonitor
    
    if !scriptMonitor.active {
        ; 开始监控
        try {
            StartScriptMonitoring()
            scriptMonitor.active := true
            ToolTip("脚本监控已开始", 10, 10)
            SetTimer(() => ToolTip(), -2000)
        } catch Error as e {
            MsgBox("启动监控失败: " . e.Message)
        }
    } else {
        ; 停止监控
        StopScriptMonitoring()
        scriptMonitor.active := false
        ToolTip("脚本监控已停止", 10, 10)
        SetTimer(() => ToolTip(), -2000)
    }
}

; 开始脚本监控
StartScriptMonitoring() {
    global scriptMonitor
    
    ; 创建事件处理器
    focusHandler := UIA.CreateAutomationEventHandler(OnElementFocused)
    invokeHandler := UIA.CreateAutomationEventHandler(OnElementInvoked)
    
    ; 添加事件监听
    UIA.AddAutomationEventHandler(focusHandler, UIA.Event.AutomationFocusChanged, scriptMonitor.coppliaElement)
    UIA.AddAutomationEventHandler(invokeHandler, UIA.Event.InvokePatternOnInvoked, scriptMonitor.coppliaElement)
    
    ; 启动鼠标监控
    SetTimer(MonitorScriptOperations, 100)
    
    ; 记录开始监控
    RecordOperation("监控开始", "开始监控用户操作", "-- 监控开始")
}

; 停止脚本监控
StopScriptMonitoring() {
    ; 移除事件处理器
    UIA.RemoveAllEventHandlers()
    
    ; 停止定时器
    SetTimer(MonitorScriptOperations, 0)
    
    ; 完成脚本文件
    FinalizeScriptFile()
    
    ; 记录停止监控
    RecordOperation("监控结束", "停止监控用户操作", "-- 监控结束")
}

; 元素焦点事件处理
OnElementFocused(sender, eventId) {
    try {
        elementName := sender.Name ? sender.Name : "[无名称]"
        elementType := sender.LocalizedType ? sender.LocalizedType : "未知类型"
        
        ; 分析焦点变化对应的脚本操作
        AnalyzeElementForScript(sender, elementName, "焦点变化")
        
    } catch {
        ; 忽略错误
    }
}

; 元素调用事件处理
OnElementInvoked(sender, eventId) {
    try {
        elementName := sender.Name ? sender.Name : "[无名称]"
        
        ; 分析调用操作对应的脚本
        AnalyzeElementForScript(sender, elementName, "元素调用")
        
    } catch {
        ; 忽略错误
    }
}

; 监控脚本相关操作
MonitorScriptOperations() {
    global scriptMonitor
    
    if !scriptMonitor.active
        return
        
    static lastClickTime := 0
    
    ; 监控鼠标点击
    if GetKeyState("LButton", "P") {
        currentTime := A_TickCount
        if (currentTime - lastClickTime > 300) {
            MouseGetPos(&x, &y, &winId)
            
            if WinExist("ahk_id " . winId) && WinGetProcessName("ahk_id " . winId) = "coppeliasim.exe" {
                try {
                    clickedElement := UIA.ElementFromPoint(x, y)
                    if clickedElement {
                        elementName := clickedElement.Name ? clickedElement.Name : "[无名称]"
                        AnalyzeElementForScript(clickedElement, elementName, "鼠标点击")
                    }
                } catch {
                    ; 忽略错误
                }
            }
            
            lastClickTime := currentTime
        }
    }
}

; 分析元素对应的脚本代码
AnalyzeElementForScript(element, elementName, operationType) {
    global scriptMonitor
    
    try {
        scriptCode := ""
        apiCall := ""
        description := ""
        
        ; 根据元素名称和类型分析对应的脚本
        if InStr(elementName, "Start") && InStr(elementName, "simulation") {
            scriptCode := "sim.startSimulation()"
            apiCall := "sim.startSimulation"
            description := "启动仿真"
            
        } else if InStr(elementName, "Stop") && InStr(elementName, "simulation") {
            scriptCode := "sim.stopSimulation()"
            apiCall := "sim.stopSimulation"
            description := "停止仿真"
            
        } else if InStr(elementName, "Suspend") && InStr(elementName, "simulation") {
            scriptCode := "sim.pauseSimulation(true)"
            apiCall := "sim.pauseSimulation"
            description := "暂停仿真"
            
        } else if InStr(elementName, "Camera") {
            if InStr(elementName, "pan") {
                scriptCode := "sim.setCameraMatrix(cameraHandle, matrix)"
                apiCall := "sim.setCameraMatrix"
                description := "设置相机平移"
            } else if InStr(elementName, "rotate") {
                scriptCode := "sim.setCameraMatrix(cameraHandle, matrix)"
                apiCall := "sim.setCameraMatrix"
                description := "设置相机旋转"
            }
            
        } else if element.Type = UIA.Type.MenuItem {
            AnalyzeMenuForScript(elementName)
            return
            
        } else if InStr(elementName, "selection") || InStr(elementName, "Selection") {
            scriptCode := "local selection = sim.getObjectSelection()"
            apiCall := "sim.getObjectSelection"
            description := "获取选中对象"
            
        } else if InStr(elementName, "Undo") {
            scriptCode := "-- 撤销操作（通过界面）"
            description := "撤销上一步操作"
            
        } else if InStr(elementName, "Redo") {
            scriptCode := "-- 重做操作（通过界面）"
            description := "重做操作"
        }
        
        ; 记录操作和对应的脚本
        if scriptCode != "" {
            RecordOperation(operationType, elementName, scriptCode, apiCall, description)
        }
        
    } catch {
        ; 忽略分析错误
    }
}

; 分析菜单对应的脚本
AnalyzeMenuForScript(menuName) {
    scriptCode := ""
    apiCall := ""
    description := ""
    
    switch menuName {
        case "File":
            description := "文件操作菜单"
        case "Edit":
            description := "编辑操作菜单"
        case "Add":
            scriptCode := "-- 添加对象示例:`nlocal shapeHandle = sim.createPrimitiveShape(sim.primitiveshape_cuboid, {1, 1, 1})"
            apiCall := "sim.createPrimitiveShape"
            description := "添加对象到场景"
        case "Simulation":
            description := "仿真设置菜单"
        case "Tools":
            description := "工具菜单"
        case "Plugins":
            scriptCode := "-- 插件操作示例:`nsim.loadPlugin('pluginName')"
            apiCall := "sim.loadPlugin"
            description := "插件操作"
    }
    
    if scriptCode != "" {
        RecordOperation("菜单操作", menuName, scriptCode, apiCall, description)
    }
}

; 记录操作
RecordOperation(operationType, elementName, scriptCode, apiCall := "", description := "") {
    global scriptMonitor
    
    ; 添加到操作历史
    operation := {
        time: A_Now,
        type: operationType,
        element: elementName,
        script: scriptCode,
        api: apiCall,
        desc: description
    }
    
    scriptMonitor.operationHistory.Push(operation)
    
    if apiCall != "" {
        scriptMonitor.apiCalls.Push(apiCall)
    }
    
    if scriptCode != "" {
        scriptMonitor.scriptSequence.Push(scriptCode)
        
        ; 写入脚本文件
        AppendToScriptFile(scriptCode, description)
    }
    
    ; 显示实时提示
    if description != "" {
        ToolTip("检测到操作: " . description, 10, 50)
        SetTimer(() => ToolTip(), -1500)
    }
}

; 添加到脚本文件
AppendToScriptFile(scriptCode, description) {
    global scriptMonitor
    
    if description != "" {
        FileAppend("`n    -- " . description . "`n", scriptMonitor.outputFile)
    }
    FileAppend("    " . scriptCode . "`n", scriptMonitor.outputFile)
}

; 完成脚本文件
FinalizeScriptFile() {
    global scriptMonitor
    
    scriptFooter := "`nend`n`n"
    scriptFooter .= "function sysCall_cleanup()`n"
    scriptFooter .= "    -- 清理代码`n"
    scriptFooter .= "end`n"
    
    FileAppend(scriptFooter, scriptMonitor.outputFile)
}

; 导出生成的脚本
ExportGeneratedScript() {
    global scriptMonitor
    
    if scriptMonitor.operationHistory.Length = 0 {
        MsgBox("暂无监控到的操作，无法导出脚本。")
        return
    }
    
    ; 生成详细的脚本分析报告
    reportFile := "CoppeliaSim_Script_Analysis_Report.txt"
    
    report := "=== CoppeliaSim 脚本分析报告 ===`n"
    report .= "生成时间: " . FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss") . "`n"
    report .= "监控操作数量: " . scriptMonitor.operationHistory.Length . "`n`n"
    
    report .= "=== API 调用序列 ===`n"
    for apiCall in scriptMonitor.apiCalls {
        report .= "• " . apiCall . "`n"
    }
    
    report .= "`n=== 操作详细记录 ===`n"
    for operation in scriptMonitor.operationHistory {
        report .= "时间: " . FormatTime(operation.time, "HH:mm:ss") . "`n"
        report .= "操作: " . operation.type . " - " . operation.element . "`n"
        if operation.desc != ""
            report .= "描述: " . operation.desc . "`n"
        if operation.script != ""
            report .= "脚本: " . operation.script . "`n"
        report .= "---`n"
    }
    
    FileDelete(reportFile)
    FileAppend(report, reportFile)
    
    MsgBox("脚本代码已导出！`n`n" .
           "Lua脚本文件: " . scriptMonitor.outputFile . "`n" .
           "分析报告: " . reportFile . "`n`n" .
           "您可以将生成的Lua脚本复制到CoppeliaSim中使用。")
}

; 显示API调用序列
ShowAPISequence() {
    global scriptMonitor
    
    if scriptMonitor.apiCalls.Length = 0 {
        MsgBox("暂无监控到的API调用。")
        return
    }
    
    apiText := "=== 检测到的 CoppeliaSim API 调用序列 ===`n`n"
    
    ; 统计API调用频率
    apiCount := Map()
    for apiCall in scriptMonitor.apiCalls {
        if apiCount.Has(apiCall) {
            apiCount[apiCall]++
        } else {
            apiCount[apiCall] := 1
        }
    }
    
    apiText .= "API调用频率统计:`n"
    for api, count in apiCount {
        apiText .= "• " . api . ": " . count . " 次`n"
    }
    
    apiText .= "`n调用序列:`n"
    for i, apiCall in scriptMonitor.apiCalls {
        apiText .= i . ". " . apiCall . "`n"
    }
    
    MsgBox(apiText, "API调用序列")
}

; 退出脚本监控器
ExitScriptMonitor() {
    global scriptMonitor
    
    if scriptMonitor.active {
        StopScriptMonitoring()
    }
    
    if scriptMonitor.operationHistory.Length > 0 {
        result := MsgBox("检测到监控数据，是否导出脚本？", "退出确认", "YesNoCancel")
        if result = "Yes" {
            ExportGeneratedScript()
        } else if result = "Cancel" {
            return
        }
    }
    
    ExitApp
}
