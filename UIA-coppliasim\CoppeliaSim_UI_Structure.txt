Type: 50032 (Window) Name: "CoppeliaSim Edu - New file - rendering: 2 ms (7.9 fps) - SIMULATION STOPPED" LocalizedType: "����" ClassName: "CMainWindow"
1: Type: 50037 (TitleBar) Value: "CoppeliaSim Edu - New file - rendering: 2 ms (7.9 fps) - SIMULATION STOPPED" LocalizedType: "������" AutomationId: "TitleBar"
1,1: Type: 50010 (MenuBar) Name: "ϵͳ" LocalizedType: "�˵���" AutomationId: "SystemMenuBar"
1,1,1: Type: 50011 (MenuItem) Name: "ϵͳ" LocalizedType: "�˵���Ŀ"
1,2: Type: 50000 (Button) Name: "��С��" LocalizedType: "��ť" AutomationId: "Minimize-Restore"
1,3: Type: 50000 (Button) Name: "���" LocalizedType: "��ť" AutomationId: "Maximize-Restore"
1,4: Type: 50000 (Button) Name: "�ر�" LocalizedType: "��ť" AutomationId: "Close"
2: Type: 50025 (Custom) LocalizedType: "�Զ���" ClassName: "QSplitter"
2,1: Type: 50025 (Custom) LocalizedType: "�Զ���" ClassName: "QSplitter"
2,1,1: Type: 50026 (Group) LocalizedType: "��" ClassName: "QWidget"
2,1,1,1: Type: 50004 (Edit) Value: "[sandboxScript:info]   Simulator launched, welcome!
[CoppeliaSim:info]   Loading model (C:/Program Files/CoppeliaRobotics/CoppeliaSimEdu/models/robots/non-mobile/ABB IRB 140.ttm).  Serialization version is 22.
[CoppeliaSim:info]   File was previously written with CoppeliaSim version 4.00.01 (rev 0)
[CoppeliaSim:info]   Model loaded.
[CoppeliaSim:info]   Deleting selection...
[CoppeliaSim:info]   done.
[CoppeliaSim:info]   Loading scene...
[CoppeliaSim:info]   Default scene was set-up.
[CoppeliaSim:info]   Loading scene (D:/Coppliasimtest/coppliasim-python.ttt).  Serialization version is 22.
[CoppeliaSim:info]   File was previously written with CoppeliaSim version 4.01.00 (rev 1)
[CoppeliaSim:info]   Scene opened.
[sandboxScript:info]   Simulation started.
[sandboxScript:info]   Simulation suspended.
[sandboxScript:info]   Simulation resumed.
[sandboxScript:info]   simulation stopping...
[sandboxScript:info]   Simulation stopped.
[sandboxScript:info]   Simulation started.
[sandboxScript:info]   simulation stopping...
[sandboxScript:info]   Simulation stopped.
[sandboxScript:info]   Simulation started.
[sandboxScript:info]   simulation stopping...
[sandboxScript:info]   Simulation stopped.
[sandboxScript:info]   Simulation started.
[sandboxScript:info]   Simulation suspended.
[sandboxScript:info]   Simulation resumed.
[sandboxScript:info]   simulation stopping...
[sandboxScript:info]   Simulation stopped.
[CoppeliaSim:info]   Loading model (C:/Program Files/CoppeliaRobotics/CoppeliaSimEdu/models/robots/non-mobile/ABB IRB 140.ttm).  Serialization version is 22.
[CoppeliaSim:info]   File was previously written with CoppeliaSim version 4.00.01 (rev 0)
[CoppeliaSim:info]   Model loaded.
[CoppeliaSim:info]   Deleting selection...
[CoppeliaSim:info]   done.
[CoppeliaSim:info]   Loading model (C:/Program Files/CoppeliaRobotics/CoppeliaSimEdu/models/robots/non-mobile/ABB IRB 140.ttm).  Serialization version is 22.
[CoppeliaSim:info]   File was previously written with CoppeliaSim version 4.00.01 (rev 0)
[CoppeliaSim:info]   Model loaded.
[sandboxScript:info]   Simulation started.
[sandboxScript:info]   simulation stopping...
[sandboxScript:info]   Simulation stopped.
[sandboxScript:info]   Simulation started.
[sandboxScript:info]   simulation stopping...
[sandboxScript:info]   Simulation stopped.
[sandboxScript:info]   Simulation started.
[sandboxScript:info]   Simulation suspended.
[sandboxScript:info]   Simulation resumed.
[sandboxScript:info]   simulation stopping...
[sandboxScript:info]   Simulation stopped." LocalizedType: "�༭" ClassName: "CStatusBar"
2,1,1,1,1: Type: 50026 (Group) LocalizedType: "��" ClassName: "QWidget"
2,1,2: Type: 50026 (Group) LocalizedType: "��" ClassName: "QWidget"
2,1,2,1: Type: 50018 (Tab) Name: "new scene" LocalizedType: "ѡ�" ClassName: "QTabBar"
2,1,2,1,1: Type: 50019 (TabItem) Name: "new scene" LocalizedType: "ѡ���Ŀ"
2,1,2,1,2: Type: 50019 (TabItem) Name: "coppliasim-python" LocalizedType: "ѡ���Ŀ"
2,1,2,2: Type: 50025 (Custom) LocalizedType: "�Զ���" ClassName: "QSplitter"
2,1,2,2,1: Type: 50026 (Group) LocalizedType: "��" ClassName: "COpenglWidget"
2,1,3: Type: 50027 (Thumb) LocalizedType: "����ͼ" ClassName: "QSplitterHandle"
2,2: Type: 50025 (Custom) LocalizedType: "�Զ���" ClassName: "QSplitter"
2,2,1: Type: 50008 (List) LocalizedType: "�б�" ClassName: "CModelListWidget"
2,2,1,1: Type: 50007 (ListItem) Name: "7 DoF manipulator.ttm" LocalizedType: "�б���Ŀ"
2,2,1,2: Type: 50007 (ListItem) Name: "ABB IRB 140.ttm" LocalizedType: "�б���Ŀ"
2,2,2: Type: 50023 (Tree) LocalizedType: "��" ClassName: "CModelFolderWidget"
2,2,2,1: Type: 50034 (Header) Name: "Model browser" LocalizedType: "��ͷ"
2,2,2,2: Type: 50024 (TreeItem) Name: "components" LocalizedType: "����Ŀ"
2,2,2,3: Type: 50024 (TreeItem) Name: "equipment" LocalizedType: "����Ŀ"
2,2,2,4: Type: 50024 (TreeItem) Name: "examples" LocalizedType: "����Ŀ"
2,2,2,5: Type: 50024 (TreeItem) Name: "furniture" LocalizedType: "����Ŀ"
2,2,2,6: Type: 50024 (TreeItem) Name: "household" LocalizedType: "����Ŀ"
2,2,2,7: Type: 50024 (TreeItem) Name: "infrastructure" LocalizedType: "����Ŀ"
2,2,2,8: Type: 50024 (TreeItem) Name: "nature" LocalizedType: "����Ŀ"
2,2,2,9: Type: 50024 (TreeItem) Name: "office items" LocalizedType: "����Ŀ"
2,2,2,10: Type: 50024 (TreeItem) Name: "other" LocalizedType: "����Ŀ"
2,2,2,11: Type: 50024 (TreeItem) Name: "people" LocalizedType: "����Ŀ"
2,2,2,12: Type: 50024 (TreeItem) Name: "robots" LocalizedType: "����Ŀ"
2,2,2,13: Type: 50024 (TreeItem) Name: "mobile" LocalizedType: "����Ŀ"
2,2,2,14: Type: 50024 (TreeItem) Name: "non-mobile" LocalizedType: "����Ŀ"
2,2,2,15: Type: 50024 (TreeItem) Name: "tools" LocalizedType: "����Ŀ"
2,2,2,16: Type: 50024 (TreeItem) Name: "vehicles" LocalizedType: "����Ŀ"
2,2,3: Type: 50027 (Thumb) LocalizedType: "����ͼ" ClassName: "QSplitterHandle"
2,3: Type: 50027 (Thumb) LocalizedType: "����ͼ" ClassName: "QSplitterHandle"
3: Type: 50010 (MenuBar) LocalizedType: "�˵���" ClassName: "QMenuBar"
3,1: Type: 50011 (MenuItem) Name: "File" LocalizedType: "�˵���Ŀ" ClassName: "QAction"
3,2: Type: 50011 (MenuItem) Name: "Edit" LocalizedType: "�˵���Ŀ" ClassName: "QAction"
3,3: Type: 50011 (MenuItem) Name: "Add" LocalizedType: "�˵���Ŀ" ClassName: "QAction"
3,4: Type: 50011 (MenuItem) Name: "Simulation" LocalizedType: "�˵���Ŀ" ClassName: "QAction"
3,5: Type: 50011 (MenuItem) Name: "Tools" LocalizedType: "�˵���Ŀ" ClassName: "QAction"
3,6: Type: 50011 (MenuItem) Name: "Plugins" LocalizedType: "�˵���Ŀ" ClassName: "QAction"
3,7: Type: 50011 (MenuItem) Name: "Add-ons" LocalizedType: "�˵���Ŀ" ClassName: "QAction"
3,8: Type: 50011 (MenuItem) Name: "Scenes" LocalizedType: "�˵���Ŀ" ClassName: "QAction"
3,9: Type: 50011 (MenuItem) Name: "Help" LocalizedType: "�˵���Ŀ" ClassName: "QAction"
4: Type: 50021 (ToolBar) Name: "Navigation" LocalizedType: "������" ClassName: "QToolBar"
4,1: Type: 50002 (CheckBox) LocalizedType: "��ѡ��" ClassName: "QToolBarExtension"
4,2: Type: 50002 (CheckBox) Name: "Camera pan" LocalizedType: "��ѡ��" ClassName: "QToolButton"
4,3: Type: 50002 (CheckBox) Name: "Camera rotate" LocalizedType: "��ѡ��" ClassName: "QToolButton"
4,4: Type: 50002 (CheckBox) Name: "Camera shift" LocalizedType: "��ѡ��" ClassName: "QToolButton"
4,5: Type: 50002 (CheckBox) Name: "Camera opening angle/view size" LocalizedType: "��ѡ��" ClassName: "QToolButton"
4,6: Type: 50000 (Button) Name: "Fit to view" LocalizedType: "��ť" ClassName: "QToolButton"
4,7: Type: 50026 (Group) LocalizedType: "��" ClassName: "QToolBarSeparator"
4,8: Type: 50002 (CheckBox) Name: "Click selection" LocalizedType: "��ѡ��" ClassName: "QToolButton"
4,9: Type: 50002 (CheckBox) Name: "Object/item shift" LocalizedType: "��ѡ��" ClassName: "QToolButton"
4,10: Type: 50002 (CheckBox) Name: "Object/item rotate" LocalizedType: "��ѡ��" ClassName: "QToolButton"
4,11: Type: 50026 (Group) LocalizedType: "��" ClassName: "QToolBarSeparator"
4,12: Type: 50000 (Button) Name: "Assemble / Disassemble" LocalizedType: "��ť" ClassName: "QToolButton"
4,13: Type: 50000 (Button) Name: "Transfer DNA to siblings" LocalizedType: "��ť" ClassName: "QToolButton"
4,14: Type: 50026 (Group) LocalizedType: "��" ClassName: "QToolBarSeparator"
4,15: Type: 50000 (Button) Name: "Undo" LocalizedType: "��ť" ClassName: "QToolButton"
4,16: Type: 50000 (Button) Name: "Redo" LocalizedType: "��ť" ClassName: "QToolButton"
4,17: Type: 50026 (Group) LocalizedType: "��" ClassName: "QToolBarSeparator"
4,18: Type: 50002 (CheckBox) Name: "Visualize and verify dynamic content (during simulation only)" LocalizedType: "��ѡ��" ClassName: "QToolButton"
4,19: Type: 50003 (ComboBox) Value: "Bullet 2.78" LocalizedType: "��Ͽ�" ClassName: "QComboBox"
4,19,1: Type: 50008 (List) LocalizedType: "�б�" ClassName: "QComboBoxListView"
4,19,1,1: Type: 50007 (ListItem) Name: "Bullet 2.78" LocalizedType: "�б���Ŀ"
4,19,1,2: Type: 50007 (ListItem) Name: "Bullet 2.83" LocalizedType: "�б���Ŀ"
4,19,1,3: Type: 50007 (ListItem) Name: "ODE" LocalizedType: "�б���Ŀ"
4,19,1,4: Type: 50007 (ListItem) Name: "Vortex" LocalizedType: "�б���Ŀ"
4,19,1,5: Type: 50007 (ListItem) Name: "Newton" LocalizedType: "�б���Ŀ"
4,20: Type: 50003 (ComboBox) Value: "Accurate (default)" LocalizedType: "��Ͽ�" ClassName: "QComboBox"
4,20,1: Type: 50008 (List) LocalizedType: "�б�" ClassName: "QComboBoxListView"
4,20,1,1: Type: 50007 (ListItem) Name: "Very accurate" LocalizedType: "�б���Ŀ"
4,20,1,2: Type: 50007 (ListItem) Name: "Accurate (default)" LocalizedType: "�б���Ŀ"
4,20,1,3: Type: 50007 (ListItem) Name: "Fast" LocalizedType: "�б���Ŀ"
4,20,1,4: Type: 50007 (ListItem) Name: "Very fast" LocalizedType: "�б���Ŀ"
4,20,1,5: Type: 50007 (ListItem) Name: "Customized" LocalizedType: "�б���Ŀ"
4,21: Type: 50003 (ComboBox) Value: "dt=50 ms (default)" LocalizedType: "��Ͽ�" ClassName: "QComboBox"
4,21,1: Type: 50008 (List) LocalizedType: "�б�" ClassName: "QComboBoxListView"
4,21,1,1: Type: 50007 (ListItem) Name: "dt=200 ms" LocalizedType: "�б���Ŀ"
4,21,1,2: Type: 50007 (ListItem) Name: "dt=100 ms" LocalizedType: "�б���Ŀ"
4,21,1,3: Type: 50007 (ListItem) Name: "dt=50 ms (default)" LocalizedType: "�б���Ŀ"
4,21,1,4: Type: 50007 (ListItem) Name: "dt=25 ms" LocalizedType: "�б���Ŀ"
4,21,1,5: Type: 50007 (ListItem) Name: "dt=10 ms" LocalizedType: "�б���Ŀ"
4,21,1,6: Type: 50007 (ListItem) Name: "dt=50.0 ms (custom)" LocalizedType: "�б���Ŀ"
4,22: Type: 50002 (CheckBox) Name: "Start/resume simulation" LocalizedType: "��ѡ��" ClassName: "QToolButton"
4,23: Type: 50002 (CheckBox) Name: "Suspend simulation" LocalizedType: "��ѡ��" ClassName: "QToolButton"
4,24: Type: 50000 (Button) Name: "Stop simulation" LocalizedType: "��ť" ClassName: "QToolButton"
5: Type: 50021 (ToolBar) Name: "Tools" LocalizedType: "������" ClassName: "QToolBar"
5,1: Type: 50002 (CheckBox) Name: "Simulation settings" LocalizedType: "��ѡ��" ClassName: "QToolButton"
5,2: Type: 50026 (Group) LocalizedType: "��" ClassName: "QToolBarSeparator"
5,3: Type: 50002 (CheckBox) Name: "Scene object properties" LocalizedType: "��ѡ��" ClassName: "QToolButton"
5,4: Type: 50002 (CheckBox) Name: "Calculation module properties" LocalizedType: "��ѡ��" ClassName: "QToolButton"
5,5: Type: 50026 (Group) LocalizedType: "��" ClassName: "QToolBarSeparator"
5,6: Type: 50002 (CheckBox) Name: "Collections" LocalizedType: "��ѡ��" ClassName: "QToolButton"
5,7: Type: 50002 (CheckBox) Name: "Scripts" LocalizedType: "��ѡ��" ClassName: "QToolButton"
5,8: Type: 50002 (CheckBox) Name: "Toggle shape edit mode (make sure to have a single shape object selected)" LocalizedType: "��ѡ��" ClassName: "QToolButton"
5,9: Type: 50002 (CheckBox) Name: "Toggle path edit mode (make sure to have a single path object selected)" LocalizedType: "��ѡ��" ClassName: "QToolButton"
5,10: Type: 50026 (Group) LocalizedType: "��" ClassName: "QToolBarSeparator"
5,11: Type: 50002 (CheckBox) Name: "Selection" LocalizedType: "��ѡ��" ClassName: "QToolButton"
5,12: Type: 50002 (CheckBox) Name: "Model browser" LocalizedType: "��ѡ��" ClassName: "QToolButton"
5,13: Type: 50002 (CheckBox) Name: "Scene hierarchy" LocalizedType: "��ѡ��" ClassName: "QToolButton"
5,14: Type: 50002 (CheckBox) Name: "Layers" LocalizedType: "��ѡ��" ClassName: "QToolButton"
5,15: Type: 50002 (CheckBox) Name: "Video recorder" LocalizedType: "��ѡ��" ClassName: "QToolButton"
5,16: Type: 50002 (CheckBox) Name: "User settings" LocalizedType: "��ѡ��" ClassName: "QToolButton"