#Requires AutoHotkey v2
#include ..\UIA-v2-1.1.0\Lib\UIA.ahk

; 启动 CoppeliaSim（使用完整路径）
Run("C:\Program Files\CoppeliaRobotics\CoppeliaSimEdu\coppeliasim.exe")
WinWaitActive("ahk_exe coppeliasim.exe")

; 等待程序完全加载
Sleep(2000)

try {
    ; 获取 CoppeliaSim 窗口元素
    hwnd := WinExist("ahk_exe coppeliasim.exe")
    if !hwnd {
        MsgBox("未找到 CoppeliaSim 窗口")
        ExitApp
    }

    element := UIA.ElementFromHandle(hwnd)

    ; 尝试查找 Start Simulation 按钮（可能有不同的名称）
    btn := ""
    buttonNames := ["Start Simulation", "开始仿真", "Start", "▶"]

    for buttonName in buttonNames {
        try {
            btn := element.FindElement({Name:buttonName})
            if btn {
                MsgBox("找到按钮: " . buttonName)
                break
            }
        } catch {
            continue
        }
    }

    ; 如果按名称找不到，尝试按类型查找按钮
    if !btn {
        try {
            ; 查找所有按钮并显示信息
            buttons := element.FindElements({Type:"Button"})
            if buttons.Length > 0 {
                buttonInfo := "找到的按钮列表:`n"
                for i, button in buttons {
                    try {
                        buttonInfo .= i . ". " . button.Name . "`n"
                    } catch {
                        buttonInfo .= i . ". [无名称按钮]`n"
                    }
                }
                MsgBox(buttonInfo)

                ; 尝试点击第一个按钮（仅作为测试）
                ; btn := buttons[1]
            } else {
                MsgBox("未找到任何按钮")
            }
        } catch Error as e {
            MsgBox("查找按钮时出错: " . e.Message)
        }
    }

    ; 点击找到的按钮
    if btn {
        try {
            btn.Click()
            MsgBox("已点击 Start Simulation 按钮")
        } catch Error as e {
            MsgBox("点击按钮时出错: " . e.Message)
        }
    } else {
        MsgBox("未找到 Start Simulation 按钮")
    }

} catch Error as e {
    MsgBox("程序执行出错: " . e.Message)
}