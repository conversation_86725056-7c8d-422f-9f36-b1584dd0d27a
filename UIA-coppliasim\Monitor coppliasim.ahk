#Requires AutoHotkey v2
#include ..\UIA-v2-1.1.0\Lib\UIA.ahk

; 启动 CoppeliaSim（使用完整路径）
Run("C:\Program Files\CoppeliaRobotics\CoppeliaSimEdu\coppeliasim.exe")
WinWaitActive("ahk_exe coppeliasim.exe")

; 等待程序完全加载
Sleep(2000)

try {
    ; 获取 CoppeliaSim 窗口元素
    hwnd := WinExist("ahk_exe coppeliasim.exe")
    if !hwnd {
        MsgBox("未找到 CoppeliaSim 窗口")
        ExitApp
    }

    element := UIA.ElementFromHandle(hwnd)

    ; 尝试查找 Start Simulation 按钮（根据UI结构分析）
    btn := ""
    buttonNames := ["Start/resume simulation", "Start Simulation", "开始仿真", "Start", "▶"]

    for buttonName in buttonNames {
        try {
            ; 尝试按名称查找（可能是Button或CheckBox类型）
            btn := element.FindElement({Name:buttonName})
            if btn {
                MsgBox("找到按钮: " . buttonName . " (类型: " . btn.LocalizedType . ")")
                break
            }
        } catch {
            continue
        }
    }

    ; 如果按名称找不到，尝试更精确的查找方法
    if !btn {
        try {
            ; 根据UI结构，Start/resume simulation 是一个CheckBox类型
            btn := element.FindElement({Type:"CheckBox", Name:"Start/resume simulation"})
            if btn {
                MsgBox("通过CheckBox类型找到开始仿真按钮")
            }
        } catch {
            ; 如果还是找不到，显示所有相关按钮信息
            try {
                ; 查找所有按钮和复选框
                buttons := element.FindElements({Type:"Button"})
                checkboxes := element.FindElements({Type:"CheckBox"})

                buttonInfo := "找到的控件列表:`n`n按钮 (Button):`n"
                for i, button in buttons {
                    try {
                        buttonInfo .= i . ". " . button.Name . "`n"
                    } catch {
                        buttonInfo .= i . ". [无名称按钮]`n"
                    }
                }

                buttonInfo .= "`n复选框 (CheckBox):`n"
                for i, checkbox in checkboxes {
                    try {
                        buttonInfo .= i . ". " . checkbox.Name . "`n"
                    } catch {
                        buttonInfo .= i . ". [无名称复选框]`n"
                    }
                }

                MsgBox(buttonInfo)
            } catch Error as e {
                MsgBox("查找控件时出错: " . e.Message)
            }
        }
    }

    ; 点击找到的按钮
    if btn {
        try {
            btn.Click()
            MsgBox("已点击 Start Simulation 按钮")
        } catch Error as e {
            MsgBox("点击按钮时出错: " . e.Message)
        }
    } else {
        MsgBox("未找到 Start Simulation 按钮")
    }

} catch Error as e {
    MsgBox("程序执行出错: " . e.Message)
}